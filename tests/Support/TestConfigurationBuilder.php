<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Tests\Support;

use PreZero\ApiBundle\Configuration;

/**
 * Test configuration builder for creating Configuration instances in tests.
 * Usage example:
 *
 * $config = TestConfigurationBuilder::create()
 *     ->withMaxPerPage(50)
 *     ->withDefaultPerPage(10)
 *     ->withArea('testArea', [
 *         'resource_path' => 'src/Dummy',
 *         'url_prefix' => '/api/v1',
 *         'max_per_page' => 75,
 *     ])
 *     ->buildConfiguration();
 *
 * @phpstan-import-type BundleConfiguration from Configuration
 * @phpstan-import-type AreaConfiguration from Configuration
 */
class TestConfigurationBuilder
{
    /**
     * @phpstan-var BundleConfiguration
     */
    private array $config;

    /**
     * @param array<string, mixed> $config
     */
    private function __construct(array $config = [])
    {
        // Initialize with sensible defaults matching Symfony's ApiBundle.php
        $this->config = array_merge([
            'max_per_page' => Configuration::DEFAULT_MAX_PER_PAGE,
            'default_per_page' => Configuration::DEFAULT_PER_PAGE,
            'use_plural_resource_names' => false,
            'consider_nullable_properties_as_optional' => false,
            'areas' => [],
        ], $config);
    }

    /**
     * Create a new builder instance with default configuration.
     */
    public static function create(): self
    {
        return new self();
    }

    /**
     * Enable or disable plural resource names globally.
     */
    public function withPluralResourceNames(bool $value = true): self
    {
        $config = $this->config;
        $config['use_plural_resource_names'] = $value;

        return new self($config);
    }

    /**
     * Add or override an area configuration.
     *
     * @phpstan-param AreaConfiguration $areaConfig
     */
    public function withArea(string $name, array $areaConfig): self
    {
        $config = $this->config;
        $config['areas'][$name] = $areaConfig;

        return new self($config);
    }

    /**
     * Build and return a Configuration instance.
     */
    public function buildConfiguration(): Configuration
    {
        return new Configuration($this->config);
    }
}
