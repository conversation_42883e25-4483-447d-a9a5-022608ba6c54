<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle\Tests\Metadata;

use PHPUnit\Framework\TestCase;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Metadata\MetadataBuilder;
use PreZero\ApiBundle\PathFormatter;
use PreZero\ApiBundle\Tests\Support\TestConfigurationBuilder;
use Symfony\Component\String\Inflector\EnglishInflector;
use Symfony\Component\String\Slugger\AsciiSlugger;

class PaginationValidationTest extends TestCase
{
    private MetadataBuilder $metadataBuilder;

    protected function setUp(): void
    {
        $configuration = TestConfigurationBuilder::create()
            ->withArea('test', ['resource_path' => 'test'])
            ->buildConfiguration();

        $this->metadataBuilder = new MetadataBuilder(
            new PathFormatter(new AsciiSlugger()),
            $configuration,
            new EnglishInflector()
        );
    }

    public function testValidPaginationConfiguration(): void
    {
        $apiResource = new ApiResource(
            area: 'test',
            operations: [
                new GetCollection(
                    controller: [TestController::class, 'getItems'],
                    maxPerPage: 50,
                    defaultPerPage: 20
                ),
            ]
        );

        $resourceMetadata = $this->metadataBuilder->buildFromApiResource(
            PaginationTestResource::class,
            $apiResource
        );

        $operationMetadata = $resourceMetadata->operations[0];

        // Should use bundle-level default max_per_page (100) for resource
        $this->assertSame(100, $resourceMetadata->maxPerPage);
        $this->assertSame(25, $resourceMetadata->defaultPerPage);

        // Should use operation-level values
        $this->assertSame(50, $operationMetadata->maxPerPage);
        $this->assertSame(20, $operationMetadata->defaultPerPage);
    }

    public function testInvalidDefaultPerPageAtResourceLevel(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Default per-page value (150) must be between 1 and max per-page value (100)');

        $apiResource = new ApiResource(
            area: 'test',
            operations: [],
            defaultPerPage: 150 // Greater than bundle max_per_page (100)
        );

        $this->metadataBuilder->buildFromApiResource(
            PaginationTestResource::class,
            $apiResource
        );
    }

    public function testInvalidDefaultPerPageAtOperationLevel(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Default per-page value (60) must be between 1 and max per-page value (50)');

        $apiResource = new ApiResource(
            area: 'test',
            operations: [
                new GetCollection(
                    controller: [TestController::class, 'getItems'],
                    maxPerPage: 50,
                    defaultPerPage: 60 // Greater than operation max_per_page (50)
                ),
            ]
        );

        $this->metadataBuilder->buildFromApiResource(
            PaginationTestResource::class,
            $apiResource
        );
    }

    public function testZeroDefaultPerPageIsInvalid(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Default per-page value (0) must be between 1 and max per-page value (100)');

        $apiResource = new ApiResource(
            area: 'test',
            operations: [],
            defaultPerPage: 0
        );

        $this->metadataBuilder->buildFromApiResource(
            PaginationTestResource::class,
            $apiResource
        );
    }

    public function testNegativeDefaultPerPageIsInvalid(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Default per-page value (-5) must be between 1 and max per-page value (100)');

        $apiResource = new ApiResource(
            area: 'test',
            operations: [],
            defaultPerPage: -5
        );

        $this->metadataBuilder->buildFromApiResource(
            PaginationTestResource::class,
            $apiResource
        );
    }

    public function testPriorityHierarchy(): void
    {
        // Global: max=100, default=25
        // Resource: max=75, default=30
        // Operation: max=60, default=40

        $apiResource = new ApiResource(
            area: 'test',
            operations: [
                new GetCollection(
                    controller: [TestController::class, 'getItems'],
                    maxPerPage: 60,
                    defaultPerPage: 40
                ),
            ],
            maxPerPage: 75,
            defaultPerPage: 30
        );

        $resourceMetadata = $this->metadataBuilder->buildFromApiResource(
            PaginationTestResource::class,
            $apiResource
        );

        $operationMetadata = $resourceMetadata->operations[0];

        // Resource should use its own values, overriding global values
        $this->assertSame(75, $resourceMetadata->maxPerPage);
        $this->assertSame(30, $resourceMetadata->defaultPerPage);

        // Operation should use its own values, overriding resource values
        $this->assertSame(60, $operationMetadata->maxPerPage);
        $this->assertSame(40, $operationMetadata->defaultPerPage);
    }
}

class TestController
{
    public function getItems(): void
    {
    }
}

class PaginationTestResource
{
    public string $id;
    public string $name;
}
