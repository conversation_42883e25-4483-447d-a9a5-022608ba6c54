<?php

/** @noinspection PhpUnhandledExceptionInspection */

declare(strict_types=1);

namespace PreZero\ApiBundle\Tests\Metadata;

use PHPUnit\Framework\TestCase;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Delete;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Operation\Put;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Metadata\FilterMetadata;
use PreZero\ApiBundle\Metadata\MetadataBuilder;
use PreZero\ApiBundle\Metadata\OperationMetadata;
use PreZero\ApiBundle\Metadata\ResourceMetadata;
use PreZero\ApiBundle\PathFormatter;
use PreZero\ApiBundle\Tests\Support\TestConfigurationBuilder;
use Symfony\Component\String\Inflector\EnglishInflector;
use Symfony\Component\String\Slugger\AsciiSlugger;

#[ApiResource(
    area: 'testArea',
    operations: [
        new Get(
            controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'getItem'],
            name: 'get_dummy',
            uriTemplate: '/dummies/{id}'
        ),
        new Post(
            controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'createItem'],
            name: 'create_dummy'
        ),
        new Put(
            controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'updateItem'],
            name: 'put_dummy',
            uriTemplate: '/dummies/{id}'
        ),
        new Patch(
            controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'patchItem'],
            name: 'patch_dummy',
            uriTemplate: '/dummies/{id}'
        ),
        new Delete(
            controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'deleteItem'],
            name: 'delete_dummy',
            uriTemplate: '/dummies/{id}'
        ),
    ],
)]
class DummyResourceForMetadata
{
    public string $id;
    public string $customId;
}

class MetadataBuilderTest extends TestCase
{
    public function testBuildFromApiResource(): void
    {
        $metadataBuilder = self::getDefaultMetadataBuilder();

        // Arrange
        $resourceClass = DummyResourceForMetadata::class;
        $reflectionClass = new \ReflectionClass($resourceClass);
        /** @var ApiResource $apiResourceAttribute */
        $apiResourceAttribute = $reflectionClass->getAttributes(ApiResource::class)[0]->newInstance();

        // Act
        $resourceMetadata = $metadataBuilder->buildFromApiResource($resourceClass, $apiResourceAttribute);

        // Assert Resource Metadata
        self::assertInstanceOf(ResourceMetadata::class, $resourceMetadata);
        self::assertSame($resourceClass, $resourceMetadata->resourceClass);
        self::assertSame('DummyResourceForMetadata', $resourceMetadata->resourceClassShortName);
        self::assertSame('testArea', $resourceMetadata->area);
        self::assertSame('id', $resourceMetadata->identifier);

        // Assert Operations Metadata
        $operations = $resourceMetadata->operations;
        self::assertCount(5, $operations);
        self::assertArrayHasKey(0, $operations);
        self::assertArrayHasKey(1, $operations);
        self::assertArrayHasKey(2, $operations);
        self::assertArrayHasKey(3, $operations);
        self::assertArrayHasKey(4, $operations);

        // Assert GET operation details
        $getOperation = $operations[0];
        self::assertInstanceOf(OperationMetadata::class, $getOperation);
        self::assertSame('get_dummy', $getOperation->name);
        self::assertSame(Get::class, $getOperation->type);
        self::assertSame('GET', $getOperation->method);
        // PathFormatter mock returns prefix + operationPath
        self::assertSame('/api/v1/dummies/{id}', $getOperation->fullUrlTemplate);

        // Assert POST operation details
        $postOperation = $operations[1];
        self::assertInstanceOf(OperationMetadata::class, $postOperation);
        self::assertSame('create_dummy', $postOperation->name);
        self::assertSame(Post::class, $postOperation->type);
        self::assertSame('POST', $postOperation->method);
        // PathFormatter mock returns prefix + resourcePath (since Post has no path)
        self::assertSame('/api/v1/dummy-resource-for-metadata', $postOperation->fullUrlTemplate);

        // Assert PUT operation details
        $putOperation = $operations[2];
        self::assertInstanceOf(OperationMetadata::class, $putOperation);
        self::assertSame('put_dummy', $putOperation->name);
        self::assertSame(Put::class, $putOperation->type);
        self::assertSame('PUT', $putOperation->method);
        // PathFormatter mock returns prefix + operationPath
        self::assertSame('/api/v1/dummies/{id}', $putOperation->fullUrlTemplate);

        // Assert PATCH operation details
        $patchOperation = $operations[3];
        self::assertInstanceOf(OperationMetadata::class, $patchOperation);
        self::assertSame('patch_dummy', $patchOperation->name);
        self::assertSame(Patch::class, $patchOperation->type);
        self::assertSame('PATCH', $patchOperation->method);
        // PathFormatter mock returns prefix + operationPath
        self::assertSame('/api/v1/dummies/{id}', $patchOperation->fullUrlTemplate);

        // Assert DELETE operation details
        $deleteOperation = $operations[4];
        self::assertInstanceOf(OperationMetadata::class, $deleteOperation);
        self::assertSame('delete_dummy', $deleteOperation->name);
        self::assertSame(Delete::class, $deleteOperation->type);
        self::assertSame('DELETE', $deleteOperation->method);
        // PathFormatter mock returns prefix + operationPath
        self::assertSame('/api/v1/dummies/{id}', $deleteOperation->fullUrlTemplate);
    }

    public function testBuildFromApiResourceThrowsExceptionWhenResourceClassDoesNotExist(): void
    {
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Could not reflect class NonExistentClass');

        $metadataBuilder = self::getDefaultMetadataBuilder();

        $metadataBuilder->buildFromApiResource(
            // @phpstan-ignore-next-line
            'NonExistentClass',
            new ApiResource(
                area: 'testArea',
                operations: []
            )
        );
    }

    public function testBuildIdentifierThrowsExceptionWhenIdentifierPropertyDoesNotExist(): void
    {
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage(
            'Could not find identifier property "nonExistentId" in class "PreZero\ApiBundle\Tests\Metadata\DummyResourceForMetadata". '.
            'If you want to use a different identifier, please specify it in the ApiResource attribute.'
        );

        $metadataBuilder = self::getDefaultMetadataBuilder();

        $resourceClass = DummyResourceForMetadata::class;
        $metadataBuilder->buildFromApiResource(
            $resourceClass,
            new ApiResource(
                area: 'testArea',
                operations: [],
                identifier: 'nonExistentId'
            )
        );
    }

    public function testBuildOperationMetadataThrowsExceptionWhenControllerClassDoesNotExist(): void
    {
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage(
            'Could not find controller action "nonExistentAction" in controller "PreZero\ApiBundle\Tests\Controller\DummyController".'
        );

        $metadataBuilder = self::getDefaultMetadataBuilder();

        $resourceClass = DummyResourceForMetadata::class;
        $metadataBuilder->buildFromApiResource(
            $resourceClass,
            new ApiResource(
                area: 'testArea',
                operations: [
                    new Get(
                        controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'nonExistentAction']
                    ),
                ]
            )
        );
    }

    public function testBuildOperationMetadataThrowsExceptionWhenControllerActionDoesNotExist(): void
    {
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Could not find controller class "NonExistentController".');

        $metadataBuilder = self::getDefaultMetadataBuilder();

        $resourceClass = DummyResourceForMetadata::class;
        $metadataBuilder->buildFromApiResource(
            $resourceClass,
            new ApiResource(
                area: 'testArea',
                operations: [
                    new Get(
                        controller: ['NonExistentController', 'getItem']
                    ),
                ]
            )
        );
    }

    public function testBuildUriTemplateThrowsExceptionWhenOperationTypeIsUnknown(): void
    {
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Unknown operation type "PreZero\ApiBundle\Tests\Metadata\DummyOperation"');

        $metadataBuilder = self::getDefaultMetadataBuilder();

        $resourceClass = DummyResourceForMetadata::class;
        $metadataBuilder->buildFromApiResource(
            $resourceClass,
            new ApiResource(
                area: 'testArea',
                operations: [
                    new DummyOperation(
                        controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'getItem'],
                        name: 'dummy',
                        summary: 'dummy',
                        description: 'dummy'
                    ),
                ]
            )
        );
    }

    public function testBuildFromApiResourceWithDifferentIdentifier(): void
    {
        $metadataBuilder = self::getDefaultMetadataBuilder();

        // Arrange
        $resourceClass = DummyResourceForMetadata::class;
        $apiResourceAttribute = new ApiResource(
            area: 'testArea',
            operations: [],
            identifier: 'customId'
        );

        // Act
        $resourceMetadata = $metadataBuilder->buildFromApiResource($resourceClass, $apiResourceAttribute);

        // Assert Resource Metadata
        self::assertInstanceOf(ResourceMetadata::class, $resourceMetadata);
        self::assertSame($resourceClass, $resourceMetadata->resourceClass);
        self::assertSame('DummyResourceForMetadata', $resourceMetadata->resourceClassShortName);
        self::assertSame('testArea', $resourceMetadata->area);
        self::assertSame('customId', $resourceMetadata->identifier);
    }

    public function testBuildFromApiResourceWithEmptyOperations(): void
    {
        $metadataBuilder = self::getDefaultMetadataBuilder();

        // Arrange
        $resourceClass = DummyResourceForMetadata::class;
        $apiResourceAttribute = new ApiResource(
            area: 'testArea',
            operations: []
        );

        // Act
        $resourceMetadata = $metadataBuilder->buildFromApiResource($resourceClass, $apiResourceAttribute);

        // Assert Resource Metadata
        self::assertInstanceOf(ResourceMetadata::class, $resourceMetadata);
        self::assertSame($resourceClass, $resourceMetadata->resourceClass);
        self::assertSame('DummyResourceForMetadata', $resourceMetadata->resourceClassShortName);
        self::assertSame('testArea', $resourceMetadata->area);
        self::assertSame('id', $resourceMetadata->identifier);
        self::assertCount(0, $resourceMetadata->operations);
    }

    public function testGetOperationThrowsExceptionWhenOperationNotFound(): void
    {
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage(
            'Operation "non_existent_operation" not found in resource "PreZero\ApiBundle\Tests\Metadata\DummyResourceForMetadata".'
        );

        // Arrange: Build metadata using the builder
        $metadataBuilder = self::getDefaultMetadataBuilder();
        $resourceClass = DummyResourceForMetadata::class;
        $reflectionClass = new \ReflectionClass($resourceClass);
        /** @var ApiResource $apiResourceAttribute */
        $apiResourceAttribute = $reflectionClass->getAttributes(ApiResource::class)[0]->newInstance();
        $resourceMetadata = $metadataBuilder->buildFromApiResource($resourceClass, $apiResourceAttribute);

        // Act & Assert Exception
        $resourceMetadata->getOperation('non_existent_operation');
    }

    public function testBuildNormalizationContext(): void
    {
        $metadataBuilder = self::getDefaultMetadataBuilder();

        $operation = new Get(
            controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'getItem'],
            normalizationContext: ['groups' => ['test']]
        );

        $resourceClass = DummyResourceForMetadata::class;
        $apiResourceAttribute = new ApiResource(
            area: 'testArea',
            operations: [$operation]
        );
        $resourceMetadata = $metadataBuilder->buildFromApiResource($resourceClass, $apiResourceAttribute);
        $operationMetadata = $resourceMetadata->operations[0];

        self::assertSame(['groups' => ['test', 'Default', 'default']], $operationMetadata->normalizationContext);
    }

    public function testBuildDenormalizationContext(): void
    {
        $metadataBuilder = self::getDefaultMetadataBuilder();

        $operation = new Get(
            controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'getItem'],
            denormalizationContext: ['groups' => ['test']]
        );

        $resourceClass = DummyResourceForMetadata::class;
        $apiResourceAttribute = new ApiResource(
            area: 'testArea',
            operations: [$operation]
        );
        $resourceMetadata = $metadataBuilder->buildFromApiResource($resourceClass, $apiResourceAttribute);
        $operationMetadata = $resourceMetadata->operations[0];

        self::assertSame(['groups' => ['test', 'Default', 'default']], $operationMetadata->denormalizationContext);
        self::assertSame(['groups' => ['test', 'Default', 'default']], $operationMetadata->denormalizationContext);
    }

    public function testBuildFiltersSetsMultipleFlag(): void
    {
        $metadataBuilder = self::getDefaultMetadataBuilder();

        $operation = new GetCollection(
            controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'getItems'],
            filters: [
                new Filter(parameterName: 'status', multiple: true),
                new Filter(parameterName: 'category'), // Default multiple: false
                new Filter(parameterName: 'tag', multiple: false),
            ]
        );

        $resourceClass = DummyResourceForMetadata::class;
        $apiResourceAttribute = new ApiResource(
            area: 'testArea',
            operations: [$operation]
        );
        $resourceMetadata = $metadataBuilder->buildFromApiResource($resourceClass, $apiResourceAttribute);
        $operationMetadata = $resourceMetadata->operations[0];

        self::assertArrayHasKey('status', $operationMetadata->filters);
        self::assertInstanceOf(FilterMetadata::class, $operationMetadata->filters['status']);
        self::assertTrue($operationMetadata->filters['status']->multiple);

        self::assertArrayHasKey('category', $operationMetadata->filters);
        self::assertInstanceOf(FilterMetadata::class, $operationMetadata->filters['category']);
        self::assertFalse($operationMetadata->filters['category']->multiple);

        self::assertArrayHasKey('tag', $operationMetadata->filters);
        self::assertInstanceOf(FilterMetadata::class, $operationMetadata->filters['tag']);
        self::assertFalse($operationMetadata->filters['tag']->multiple);
    }

    public function testMetadataBuilderPassesPriorityFromOperationToMetadata(): void
    {
        // Arrange
        $metadataBuilder = self::getDefaultMetadataBuilder();
        $resourceClass = DummyResourceForMetadata::class;
        $apiResourceAttribute = new ApiResource(
            area: 'testArea',
            operations: [
                new Get(
                    controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'getItem'],
                    name: 'get_dummy_with_priority',
                    routePriority: 42
                ),
                new Post(
                    controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'createItem'],
                    name: 'create_dummy_default_priority'
                    // No priority specified, should default to 0
                ),
            ],
        );

        // Act
        $resourceMetadata = $metadataBuilder->buildFromApiResource($resourceClass, $apiResourceAttribute);
        $getOperationMetadata = $resourceMetadata->operations[0];
        $postOperationMetadata = $resourceMetadata->operations[1];

        // Assert
        self::assertSame(42, $getOperationMetadata->routePriority);
        self::assertSame(0, $postOperationMetadata->routePriority);
    }

    public function testBuildUriTemplateWithGlobalPluralNaming(): void
    {
        $configuration = TestConfigurationBuilder::create()
            ->withPluralResourceNames(true)
            ->withArea('testArea', ['resource_path' => 'test'])
            ->buildConfiguration();

        $metadataBuilder = new MetadataBuilder(
            new PathFormatter(new AsciiSlugger()),
            $configuration,
            new EnglishInflector()
        );

        $resourceClass = DummyResourceForMetadata::class;
        $apiResourceAttribute = new ApiResource(
            area: 'testArea',
            operations: [
                new GetCollection(
                    controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'getItems'],
                    name: 'get_dummies'
                ),
                new Get(
                    controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'getItem'],
                    name: 'get_dummy'
                ),
            ],
        );

        $resourceMetadata = $metadataBuilder->buildFromApiResource($resourceClass, $apiResourceAttribute);

        // Collection operation should use plural form
        $collectionOperation = $resourceMetadata->operations[0];
        $this->assertSame('/dummy-resource-for-metadatas', $collectionOperation->uriTemplate);

        // Item operation should use plural form
        $itemOperation = $resourceMetadata->operations[1];
        $this->assertSame('/dummy-resource-for-metadatas/{id}', $itemOperation->uriTemplate);
    }

    public function testBuildUriTemplateWithAreaPluralNamingOverride(): void
    {
        $configuration = TestConfigurationBuilder::create()
            ->withPluralResourceNames(false) // Global setting is singular
            ->withArea('testArea', [
                'resource_path' => 'test',
                'use_plural_resource_names' => true, // Area overrides to plural
            ])
            ->buildConfiguration();

        $metadataBuilder = new MetadataBuilder(
            new PathFormatter(new AsciiSlugger()),
            $configuration,
            new EnglishInflector()
        );

        $resourceClass = DummyResourceForMetadata::class;
        $apiResourceAttribute = new ApiResource(
            area: 'testArea',
            operations: [
                new GetCollection(
                    controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'getItems'],
                    name: 'get_dummies'
                ),
            ],
        );

        $resourceMetadata = $metadataBuilder->buildFromApiResource($resourceClass, $apiResourceAttribute);

        // Should use plural form due to area override
        $operation = $resourceMetadata->operations[0];
        $this->assertSame('/dummy-resource-for-metadatas', $operation->uriTemplate);
    }

    public function testBuildUriTemplateWithDefaultSingularNaming(): void
    {
        $metadataBuilder = self::getDefaultMetadataBuilder();

        $resourceClass = DummyResourceForMetadata::class;
        $apiResourceAttribute = new ApiResource(
            area: 'testArea',
            operations: [
                new GetCollection(
                    controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'getItems'],
                    name: 'get_dummies'
                ),
            ],
        );

        $resourceMetadata = $metadataBuilder->buildFromApiResource($resourceClass, $apiResourceAttribute);

        // Should use singular form (default behavior)
        $operation = $resourceMetadata->operations[0];
        $this->assertSame('/dummy-resource-for-metadata', $operation->uriTemplate);
    }

    public function testBuildUriTemplateWithSimpleResourceNames(): void
    {
        $configuration = TestConfigurationBuilder::create()
            ->withPluralResourceNames(true)
            ->withArea('testArea', ['resource_path' => 'test'])
            ->buildConfiguration();

        $metadataBuilder = new MetadataBuilder(
            new PathFormatter(new AsciiSlugger()),
            $configuration,
            new EnglishInflector()
        );

        // Test with a simple resource name
        $apiResourceAttribute = new ApiResource(
            area: 'testArea',
            operations: [
                new GetCollection(
                    controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'getItems'],
                    name: 'get_users'
                ),
                new Get(
                    controller: [\PreZero\ApiBundle\Tests\Controller\DummyController::class, 'getItem'],
                    name: 'get_user'
                ),
            ],
            name: 'User',
        );

        $resourceMetadata = $metadataBuilder->buildFromApiResource(DummyResourceForMetadata::class, $apiResourceAttribute);

        // Collection operation should use plural form
        $collectionOperation = $resourceMetadata->operations[0];
        $this->assertSame('/users', $collectionOperation->uriTemplate);

        // Item operation should use plural form
        $itemOperation = $resourceMetadata->operations[1];
        $this->assertSame('/users/{id}', $itemOperation->uriTemplate);
    }

    private static function getDefaultMetadataBuilder(): MetadataBuilder
    {
        $configuration = TestConfigurationBuilder::create()
            ->withArea('testArea', ['resource_path' => 'test', 'url_prefix' => '/api/v1'])
            ->buildConfiguration();

        return new MetadataBuilder(
            new PathFormatter(new AsciiSlugger()),
            $configuration,
            new EnglishInflector()
        );
    }
}
