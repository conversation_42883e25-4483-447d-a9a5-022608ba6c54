<?php

declare(strict_types=1);

namespace PreZero\ApiBundle\Tests;

use PHPUnit\Framework\TestCase;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Metadata\MetadataBuilder;
use PreZero\ApiBundle\PathFormatter;
use PreZero\ApiBundle\Tests\Support\TestConfigurationBuilder;
use Symfony\Component\String\Inflector\EnglishInflector;
use Symfony\Component\String\Slugger\AsciiSlugger;

class PathParameterConstraintTest extends TestCase
{
    private MetadataBuilder $metadataBuilder;

    protected function setUp(): void
    {
        $configuration = TestConfigurationBuilder::create()
            ->withArea('testArea', ['resource_path' => 'test'])
            ->buildConfiguration();

        $this->metadataBuilder = new MetadataBuilder(
            new PathFormatter(new AsciiSlugger()),
            $configuration,
            new EnglishInflector()
        );
    }

    public function testPathParameterWithConstraintGeneratesUrlRequirements(): void
    {
        // Arrange
        $apiResource = new ApiResource(
            area: 'testArea',
            operations: [
                new Get(
                    controller: ['PreZero\\ApiBundle\\Tests\\Controller\\DummyController', 'getItem'],
                    pathParameters: [
                        new PathParameter('id', 'integer', 'The item ID', '\d+'),
                    ]
                ),
            ]
        );

        // Act
        $resourceMetadata = $this->metadataBuilder->buildFromApiResource(
            TestResourceWithConstraints::class,
            $apiResource
        );

        // Assert
        $operation = $resourceMetadata->operations[0];
        self::assertSame(['id' => '\d+'], $operation->urlRequirements);
        self::assertCount(1, $operation->pathParameters);
        self::assertSame('id', $operation->pathParameters[0]->name);
        self::assertSame('\d+', $operation->pathParameters[0]->constraint);
    }

    public function testPathParameterWithoutConstraintDoesNotGenerateUrlRequirements(): void
    {
        // Arrange
        $apiResource = new ApiResource(
            area: 'testArea',
            operations: [
                new Get(
                    controller: ['PreZero\\ApiBundle\\Tests\\Controller\\DummyController', 'getItem'],
                    pathParameters: [
                        new PathParameter('slug', 'string', 'The item slug'),
                    ]
                ),
            ]
        );

        // Act
        $resourceMetadata = $this->metadataBuilder->buildFromApiResource(
            TestResourceWithConstraints::class,
            $apiResource
        );

        // Assert
        $operation = $resourceMetadata->operations[0];
        self::assertSame([], $operation->urlRequirements);
        self::assertCount(1, $operation->pathParameters);
        self::assertSame('slug', $operation->pathParameters[0]->name);
        self::assertNull($operation->pathParameters[0]->constraint);
    }

    public function testMultiplePathParametersWithMixedConstraints(): void
    {
        // Arrange
        $apiResource = new ApiResource(
            area: 'testArea',
            operations: [
                new Get(
                    controller: ['PreZero\\ApiBundle\\Tests\\Controller\\DummyController', 'getItem'],
                    uriTemplate: '/items/{id}/comments/{commentId}',
                    pathParameters: [
                        new PathParameter('id', 'integer', 'The item ID', '\d+'),
                        new PathParameter('commentId', 'string', 'The comment ID', '[a-f0-9-]{36}'),
                    ]
                ),
            ]
        );

        // Act
        $resourceMetadata = $this->metadataBuilder->buildFromApiResource(
            TestResourceWithConstraints::class,
            $apiResource
        );

        // Assert
        $operation = $resourceMetadata->operations[0];
        $expectedUrlRequirements = [
            'id' => '\d+',
            'commentId' => '[a-f0-9-]{36}',
        ];
        self::assertSame($expectedUrlRequirements, $operation->urlRequirements);
        self::assertCount(2, $operation->pathParameters);
    }

    public function testPathParameterConstraintWithSpecialCharacters(): void
    {
        // Arrange
        $apiResource = new ApiResource(
            area: 'testArea',
            operations: [
                new Get(
                    controller: ['PreZero\\ApiBundle\\Tests\\Controller\\DummyController', 'getItem'],
                    pathParameters: [
                        new PathParameter('code', 'string', 'The product code', '[A-Z]{2}-\d{4}'),
                    ]
                ),
            ]
        );

        // Act
        $resourceMetadata = $this->metadataBuilder->buildFromApiResource(
            TestResourceWithConstraints::class,
            $apiResource
        );

        // Assert
        $operation = $resourceMetadata->operations[0];
        self::assertSame(['code' => '[A-Z]{2}-\d{4}'], $operation->urlRequirements);
    }

    public function testPostOperationWithPathParameterConstraints(): void
    {
        // Arrange
        $apiResource = new ApiResource(
            area: 'testArea',
            operations: [
                new Post(
                    controller: ['PreZero\\ApiBundle\\Tests\\Controller\\DummyController', 'createSubItem'],
                    uriTemplate: '/items/{parentId}/sub-items',
                    pathParameters: [
                        new PathParameter('parentId', 'integer', 'The parent item ID', '\d+'),
                    ]
                ),
            ]
        );

        // Act
        $resourceMetadata = $this->metadataBuilder->buildFromApiResource(
            TestResourceWithConstraints::class,
            $apiResource
        );

        // Assert
        $operation = $resourceMetadata->operations[0];
        self::assertSame(['parentId' => '\d+'], $operation->urlRequirements);
        self::assertCount(1, $operation->pathParameters);
        self::assertSame('parentId', $operation->pathParameters[0]->name);
        self::assertSame('\d+', $operation->pathParameters[0]->constraint);
    }

    public function testOperationWithNoPathParametersGeneratesEmptyUrlRequirements(): void
    {
        // Arrange
        $apiResource = new ApiResource(
            area: 'testArea',
            operations: [
                new Post(
                    controller: ['PreZero\\ApiBundle\\Tests\\Controller\\DummyController', 'createItem'],
                ),
            ]
        );

        // Act
        $resourceMetadata = $this->metadataBuilder->buildFromApiResource(
            TestResourceWithConstraints::class,
            $apiResource
        );

        // Assert
        $operation = $resourceMetadata->operations[0];
        self::assertSame([], $operation->urlRequirements);
        self::assertCount(0, $operation->pathParameters);
    }

    public function testMixedPathParametersWithAndWithoutConstraints(): void
    {
        // Arrange
        $apiResource = new ApiResource(
            area: 'testArea',
            operations: [
                new Get(
                    controller: ['PreZero\\ApiBundle\\Tests\\Controller\\DummyController', 'getItem'],
                    uriTemplate: '/categories/{categorySlug}/items/{itemId}',
                    pathParameters: [
                        new PathParameter('categorySlug', 'string', 'Category slug'), // No constraint
                        new PathParameter('itemId', 'integer', 'Item ID', '\d+'), // With constraint
                    ]
                ),
            ]
        );

        // Act
        $resourceMetadata = $this->metadataBuilder->buildFromApiResource(
            TestResourceWithConstraints::class,
            $apiResource
        );

        // Assert
        $operation = $resourceMetadata->operations[0];
        // Only the parameter with constraint should appear in urlRequirements
        self::assertSame(['itemId' => '\d+'], $operation->urlRequirements);
        self::assertCount(2, $operation->pathParameters);

        // Verify first parameter (no constraint)
        self::assertSame('categorySlug', $operation->pathParameters[0]->name);
        self::assertNull($operation->pathParameters[0]->constraint);

        // Verify second parameter (with constraint)
        self::assertSame('itemId', $operation->pathParameters[1]->name);
        self::assertSame('\d+', $operation->pathParameters[1]->constraint);
    }
}

class TestResourceWithConstraints
{
    public string $id;
}
