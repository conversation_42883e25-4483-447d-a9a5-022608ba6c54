{"name": "prezero/api-bundle", "description": "Opinionated API Bundle", "type": "library", "license": "proprietary", "autoload": {"psr-4": {"PreZero\\ApiBundle\\": "src/"}}, "autoload-dev": {"psr-4": {"PreZero\\ApiBundle\\Tests\\": "tests/"}}, "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.4", "symfony/http-kernel": "^7.2", "symfony/routing": "^7.2", "symfony/framework-bundle": "^7.2", "vuryss/dokky": "^1.6.0", "symfony/validator": "^7.2", "symfony/serializer": "^7.2", "symfony/expression-language": "^7.2", "symfony/security-core": "^7.2", "vuryss/serializer": "^1.9", "symfony/console": "^7.2", "symfony/string": "^7.3"}, "require-dev": {"phpstan/phpstan": "^2.1", "friendsofphp/php-cs-fixer": "^3.73", "captainhook/captainhook": "^5.25", "captainhook/hook-installer": "^1.0", "ramsey/conventional-commits": "^1.6", "phpunit/phpunit": "^12.0"}, "scripts": {"test": "phpunit", "test-coverage": "XDEBUG_MODE=coverage phpunit --coverage-html coverage"}, "config": {"allow-plugins": {"captainhook/hook-installer": true}}}