<?php

declare(strict_types=1);

namespace Pre<PERSON>ero\ApiBundle\Metadata;

use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\Delete;
use PreZero\ApiBundle\Attribute\Operation\Get;
use PreZero\ApiBundle\Attribute\Operation\GetArray;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Operation\Operation;
use PreZero\ApiBundle\Attribute\Operation\Patch;
use PreZero\ApiBundle\Attribute\Operation\Post;
use PreZero\ApiBundle\Attribute\Operation\Put;
use PreZero\ApiBundle\Configuration;
use PreZero\ApiBundle\Enum\Pagination;
use PreZero\ApiBundle\Exception\ApiBundleException;
use PreZero\ApiBundle\PathFormatter;
use Symfony\Component\String\Inflector\InflectorInterface;
use Symfony\Component\Validator\Constraints\Choice;

readonly class MetadataBuilder
{
    public function __construct(
        private PathFormatter $pathFormatter,
        private Configuration $configuration,
        private InflectorInterface $inflector,
    ) {
    }

    /**
     * @param class-string $resourceClass
     *
     * @throws ApiBundleException
     */
    public function buildFromApiResource(string $resourceClass, ApiResource $apiResource): ResourceMetadata
    {
        try {
            $reflectionClass = new \ReflectionClass($resourceClass);
        } catch (\ReflectionException $e) { // @phpstan-ignore catch.neverThrown
            throw new \RuntimeException(sprintf('Could not reflect class %s', $resourceClass), 0, $e);
        }

        $bundleConfiguration = $this->configuration->getBundleConfiguration();
        $apiAreaConfiguration = $this->configuration->getAreaConfiguration($apiResource->area);
        $maxPerPage = $bundleConfiguration['max_per_page'];
        $defaultPerPage = $bundleConfiguration['default_per_page'];

        // Resolve max per page with priority: global → area → resource
        if (isset($apiAreaConfiguration['max_per_page'])) {
            $maxPerPage = $apiAreaConfiguration['max_per_page'];
        }

        if (isset($apiResource->maxPerPage)) {
            $maxPerPage = $apiResource->maxPerPage;
        }

        // Resolve default per page with priority: global → area → resource
        if (isset($apiAreaConfiguration['default_per_page'])) {
            $defaultPerPage = $apiAreaConfiguration['default_per_page'];
        }

        if (isset($apiResource->defaultPerPage)) {
            $defaultPerPage = $apiResource->defaultPerPage;
        }

        // Validate that default per page is within valid range
        if ($defaultPerPage < 1 || $defaultPerPage > $maxPerPage) {
            throw new \InvalidArgumentException(
                sprintf(
                    'Default per-page value (%d) must be between 1 and max per-page value (%d) for resource "%s"',
                    $defaultPerPage,
                    $maxPerPage,
                    $resourceClass
                )
            );
        }

        $resourceMetadata = new ResourceMetadata(
            resourceClassNamespace: $reflectionClass->getNamespaceName(),
            resourceClass: $resourceClass,
            resourceClassShortName: $reflectionClass->getShortName(),
            name: $apiResource->name ?? $reflectionClass->getShortName(),
            area: $apiResource->area,
            identifier: $this->buildIdentifier($reflectionClass, $apiResource),
            pathPrefix: $apiAreaConfiguration['url_prefix'] ?? $apiResource->area.'-api',
            tag: $apiResource->tag,
            security: $apiResource->security,
            operations: [],
            maxPerPage: $maxPerPage,
            defaultPerPage: $defaultPerPage,
        );

        $resourceOperations = [];

        foreach ($apiResource->operations as $index => $operation) {
            $resourceOperations[] = $this->buildOperationMetadata($reflectionClass, $resourceMetadata, $index, $operation);
        }

        return $resourceMetadata->withOperations($resourceOperations);
    }

    /**
     * @param \ReflectionClass<object> $reflectionClass
     */
    private function buildIdentifier(\ReflectionClass $reflectionClass, ApiResource $apiResource): ?string
    {
        if (null === $apiResource->identifier) {
            return null;
        }

        if (!$reflectionClass->hasProperty($apiResource->identifier)) {
            throw new \RuntimeException(sprintf(
                'Could not find identifier property "%s" in class "%s". If you want to use a different identifier, please specify it in the ApiResource attribute.',
                $apiResource->identifier,
                $reflectionClass->getName(),
            ));
        }

        return $apiResource->identifier;
    }

    /**
     * @param \ReflectionClass<object> $reflectionClass
     */
    private function buildOperationMetadata(
        \ReflectionClass $reflectionClass,
        ResourceMetadata $resourceMetadata,
        int $index,
        Operation $operation,
    ): OperationMetadata {
        $this->validateController($operation->controller);

        $urlTemplate = $operation->uriTemplate ?? $this->buildUriTemplate($resourceMetadata, $operation);

        $pathParameters = $this->preparePathParameters($reflectionClass, $resourceMetadata, $operation);
        $urlRequirements = $this->buildUrlRequirementsFromPathParameters($pathParameters);

        return new OperationMetadata(
            name: $operation->name ?? $this->constructOperationName($resourceMetadata, $reflectionClass, $operation, $index),
            controller: $operation->controller,
            security: $operation->security,
            summary: $operation->summary,
            description: $operation->description,
            type: $operation::class,
            method: $operation::HTTP_METHOD,
            uriTemplate: $urlTemplate,
            fullUrlTemplate: '/'.trim($resourceMetadata->pathPrefix, '/').$urlTemplate,
            urlRequirements: $urlRequirements,
            routePriority: $operation->routePriority,
            pathParameters: $pathParameters,
            queryParameters: $this->prepareQueryParameters($operation, $resourceMetadata),
            denormalizationContext: $this->buildDenormalizationContext($operation),
            filters: $this->buildFilters($operation),
            input: $operation->input,
            requestType: $operation->requestType,
            requestDescription: $operation->requestDescription,
            headers: array_map(HeaderMetadata::fromAttribute(...), $operation->additionalRequestHeaders),
            requestOpenApiSchemaName: $operation->requestOpenApiSchemaName,
            pagination: $operation->pagination,
            maxPerPage: $this->resolveOperationMaxPerPage($operation, $resourceMetadata),
            defaultPerPage: $this->resolveOperationDefaultPerPage($operation, $resourceMetadata),
            responses: array_map(ResponseMetadata::fromAttribute(...), $operation->responses),
            responseDescription: $operation->responseDescription,
            normalizationContext: $this->buildNormalizationContext($operation),
            successHttpCode: $operation->successHttpCode,
            output: $operation->output,
            responseType: $operation->responseType,
            responseOpenApiSchemaName: $operation->responseOpenApiSchemaName,
            responseCodesLogLevel: $operation->responseCodesLogLevel,
        );
    }

    private function buildUriTemplate(ResourceMetadata $resourceMetadata, Operation $operation): string
    {
        $resourceName = $resourceMetadata->name;

        // Apply pluralization if enabled
        if ($this->shouldUsePluralResourceNames($resourceMetadata->area)) {
            $resourceName = $this->inflector->pluralize($resourceName)[0] ?? $resourceName;
        }

        $formattedResourceName = $this->pathFormatter->format($resourceName);

        return match ($operation::class) {
            Get::class, Delete::class, Put::class, Patch::class => sprintf(
                null === $resourceMetadata->identifier ? '/%s' : '/%s/{%s}',
                $formattedResourceName,
                $resourceMetadata->identifier ?? ''
            ),
            GetCollection::class, GetArray::class, Post::class => sprintf('/%s', $formattedResourceName),
            default => throw new \RuntimeException(sprintf('Unknown operation type "%s"', $operation::class)),
        };
    }

    /**
     * @param \ReflectionClass<object> $reflectionClass
     */
    public function constructOperationName(
        ResourceMetadata $resourceMetadata,
        \ReflectionClass $reflectionClass,
        Operation $operation,
        int $index,
    ): string {
        return sprintf(
            '%s_%s_%s_%s',
            $resourceMetadata->area,
            $reflectionClass->getShortName(),
            match ($operation::class) {
                Get::class => 'get',
                GetCollection::class => 'get_collection',
                GetArray::class => 'get_array',
                Post::class => 'post',
                Patch::class => 'patch',
                Put::class => 'put',
                Delete::class => 'delete',
                default => throw new \RuntimeException(sprintf('Unknown operation type "%s"', $operation::class)),
            },
            $index,
        );
    }

    /**
     * @param \ReflectionClass<object> $reflectionClass
     *
     * @return array<PathParameterMetadata>
     */
    private function preparePathParameters(
        \ReflectionClass $reflectionClass,
        ResourceMetadata $resourceMetadata,
        Operation $operation,
    ): array {
        // Not explicitly specified path parameters - add the identifier to operations that operate on a single resource
        if (null === $operation->pathParameters) {
            if (
                (
                    $operation instanceof Get
                    || $operation instanceof Patch
                    || $operation instanceof Put
                    || $operation instanceof Delete
                )
                && null !== $resourceMetadata->identifier
            ) {
                try {
                    $reflectionProperty = $reflectionClass->getProperty($resourceMetadata->identifier);
                } catch (\ReflectionException $e) {
                    throw new \RuntimeException(
                        sprintf(
                            'Could not find identifier property "%s" in class "%s". If you want to use a different identifier, please specify it in the ApiResource attribute.',
                            $resourceMetadata->identifier,
                            $reflectionClass->getName(),
                        ),
                        code: $e->getCode(),
                        previous: $e
                    );
                }

                $type = $reflectionProperty->getType();

                return [
                    new PathParameterMetadata(
                        name: $resourceMetadata->identifier,
                        type: null !== $type ? (string) $type : 'string',
                        description: 'Identifier of the resource',
                    ),
                ];
            }

            return [];
        }

        // Explicitly specified path parameters prevent the default ones from being added
        $pathParameters = [];

        foreach ($operation->pathParameters as $pathParameter) {
            $pathParameters[] = new PathParameterMetadata(
                name: $pathParameter->name,
                type: $pathParameter->type,
                description: $pathParameter->description,
                constraint: $pathParameter->constraint,
            );
        }

        return $pathParameters;
    }

    /**
     * @return array<QueryParameterMetadata>
     */
    private function prepareQueryParameters(
        Operation $operation,
        ResourceMetadata $resourceMetadata,
    ): array {
        $queryParameters = [];

        // Add page and per-page query parameters to collection operations
        if (Pagination::PAGE_BASED === $operation->pagination && !$operation->omitDefaultQueryParameters) {
            $queryParameters[] = new QueryParameterMetadata(
                name: 'page',
                type: 'integer',
                required: false,
                description: 'Page number',
            );

            $queryParameters[] = new QueryParameterMetadata(
                name: 'per-page',
                type: 'integer',
                required: false,
                description: 'Number of items per page',
                minimum: 1,
                maximum: $this->resolveOperationMaxPerPage($operation, $resourceMetadata),
            );
        }

        if (Pagination::CURSOR === $operation->pagination && !$operation->omitDefaultQueryParameters) {
            $queryParameters[] = new QueryParameterMetadata(
                name: 'next-page-token',
                type: 'string',
                required: false,
                description: 'Token to get the next page of results. If not set, the first page will be returned.',
            );

            $queryParameters[] = new QueryParameterMetadata(
                name: 'per-page',
                type: 'integer',
                required: false,
                description: 'Number of items per page',
                minimum: 1,
                maximum: $this->resolveOperationMaxPerPage($operation, $resourceMetadata),
            );
        }

        foreach ($operation->queryParameters as $queryParameter) {
            $queryParameters[] = new QueryParameterMetadata(
                name: $queryParameter->name,
                type: $queryParameter->type,
                required: $queryParameter->required,
                description: $queryParameter->description,
                format: $queryParameter->format,
                enumValues: $queryParameter->enumValues,
                multipleValues: $queryParameter->multipleValues,
            );
        }

        return $queryParameters;
    }

    /**
     * @return array<string, scalar|string[]>
     */
    private function buildNormalizationContext(Operation $operation): array
    {
        $context = $operation->normalizationContext;

        if (
            array_key_exists('groups', $context)
            && is_array($context['groups'])
            && [] !== $context['groups']
        ) {
            $context['groups'] = array_merge($context['groups'], ['Default', 'default']);
        }

        /** @phpstan-var array<string, scalar|string[]> $context */
        return $context;
    }

    /**
     * @return array<string, scalar|string[]>
     */
    private function buildDenormalizationContext(Operation $operation): array
    {
        $context = $operation->denormalizationContext;

        if (
            array_key_exists('groups', $context)
            && is_array($context['groups'])
            && [] !== $context['groups']
        ) {
            $context['groups'] = array_merge($context['groups'], ['Default', 'default']);
        }

        /** @phpstan-var array<string, scalar|string[]> $context */
        return $context;
    }

    /**
     * @return FilterMetadata[]
     */
    private function buildFilters(Operation $operation): array
    {
        $filtersMetadata = [];

        foreach ($operation->filters as $filter) {
            $validators = $filter->validators;

            if (null !== $filter->parameterEnumValues && count($filter->parameterEnumValues) > 0) {
                $validators[] = new Choice(choices: $filter->parameterEnumValues);
            }

            $filtersMetadata[$filter->parameterName] = new FilterMetadata(
                parameterName: $filter->parameterName,
                filterType: $filter->filterType,
                fieldName: $filter->fieldName,
                required: $filter->required,
                parameterType: $filter->parameterType,
                parameterDescription: $filter->parameterDescription,
                parameterFormat: $filter->parameterFormat,
                parameterEnumValues: $filter->parameterEnumValues,
                validators: $validators,
                multiple: $filter->multiple,
            );
        }

        return $filtersMetadata;
    }

    /**
     * @param array<PathParameterMetadata> $pathParameters
     *
     * @return array<string, string>
     */
    private function buildUrlRequirementsFromPathParameters(array $pathParameters): array
    {
        $urlRequirements = [];

        foreach ($pathParameters as $pathParameter) {
            if (null !== $pathParameter->constraint) {
                $urlRequirements[$pathParameter->name] = $pathParameter->constraint;
            }
        }

        return $urlRequirements;
    }

    /**
     * @param class-string|array{class-string, string} $controller
     */
    private function validateController(array|string $controller): void
    {
        if (is_string($controller) && !class_exists($controller)) {
            throw new \RuntimeException(sprintf('Could not find controller class "%s".', $controller));
        }

        if (is_array($controller)) {
            if (!class_exists($controller[0])) {
                throw new \RuntimeException(sprintf('Could not find controller class "%s".', $controller[0]));
            }

            if (!method_exists($controller[0], $controller[1])) {
                throw new \RuntimeException(sprintf('Could not find controller action "%s" in controller "%s".', $controller[1], $controller[0]));
            }
        }
    }

    private function resolveOperationMaxPerPage(Operation $operation, ResourceMetadata $resourceMetadata): int
    {
        return $operation->maxPerPage ?? $resourceMetadata->maxPerPage;
    }

    private function resolveOperationDefaultPerPage(Operation $operation, ResourceMetadata $resourceMetadata): int
    {
        $maxPerPage = $this->resolveOperationMaxPerPage($operation, $resourceMetadata);
        $defaultPerPage = $operation->defaultPerPage ?? $resourceMetadata->defaultPerPage;

        // Validate that operation-level default per page is within valid range
        if ($defaultPerPage < 1 || $defaultPerPage > $maxPerPage) {
            throw new \InvalidArgumentException(
                sprintf(
                    'Default per-page value (%d) must be between 1 and max per-page value (%d) for operation "%s"',
                    $defaultPerPage,
                    $maxPerPage,
                    $operation->name ?? 'unnamed'
                )
            );
        }

        return $defaultPerPage;
    }

    private function shouldUsePluralResourceNames(string $area): bool
    {
        $bundleConfiguration = $this->configuration->getBundleConfiguration();
        $areaConfiguration = $this->configuration->getAreaConfiguration($area);
        $usePlural = $bundleConfiguration['use_plural_resource_names'];

        // Area configuration overrides global configuration
        if (isset($areaConfiguration['use_plural_resource_names'])) {
            $usePlural = $areaConfiguration['use_plural_resource_names'];
        }

        return $usePlural;
    }
}
